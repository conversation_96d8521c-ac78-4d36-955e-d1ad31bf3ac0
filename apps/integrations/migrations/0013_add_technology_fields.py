# Migration to add missing fields to Technology table

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0012_populate_technology_table"),
    ]

    operations = [
        # Add the missing fields to Technology table
        migrations.AddField(
            model_name="technology",
            name="technology_id",
            field=models.Char<PERSON>ield(max_length=255, unique=True, null=True),
        ),
        migrations.AddField(
            model_name="technology",
            name="category",
            field=models.CharField(max_length=255, default="asset_source"),
        ),
        migrations.AddField(
            model_name="technology",
            name="internal",
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name="technology",
            name="created_at",
            field=models.DateTime<PERSON>ield(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name="technology",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, null=True),
        ),
    ]
