# Migration to populate Technology table with data

from django.db import migrations
import uuid


def populate_technology_data(apps, schema_editor):
    """Populate Technology table with data."""
    Technology = apps.get_model("integrations", "Technology")

    # Technology ID to name mapping from migration 0010
    technology_names = {
        "absolute": "Absolute",
        "azure_ad": "Microsoft Entra ID (Azure AD)",
        "carbon_black": "Carbon Black",
        "cb_cloud": "Carbon Black Cloud Endpoint Standard",
        "cb_threat_hunter": "Carbon Black Threat Hunter",
        "cisco_duo": "Duo",
        "cisco_ise": "Cisco Ise AssetView",
        "commvault": "Commvault",
        "cortex_xdr": "Palo Alto Cortex XDR",
        "crowdstrike_falcon": "CrowdStrike Falcon",
        "cyberark_epm": "CyberArk Endpoint Privilege Manager",
        "cylance": "Cylance",
        "defender_atp": "Microsoft Defender for Endpoint",
        "extrahop_revealx_360": "ExtraHop RevealX 360",
        "falcon_em": "CrowdStrike Discover",
        "freshworks_service": "Freshworks Freshservice",
        "import_hosts": "Import Hosts",
        "infoblox_ddi": "Infoblox DDI",
        "ivanti_neurons": "Ivanti Neurons",
        "ivanti_pm": "Ivanti Patch Management",
        "jamf_pro": "Jamf Pro",
        "ms_intune": "Microsoft Intune",
        "netapp_ontap": "NetApp ONTAP",
        "qualys_gav": "Qualys Global AssetView",
        "qualys_vmpc": "Qualys VMDR",
        "rapid7_insightvm": "Rapid7 InsightVM",
        "s1_ranger": "SentinelOne Ranger",
        "sentinel_one": "SentinelOne",
        "servicenow_cmdb": "ServiceNow CMDB",
        "sevco_io": "Sevco",
        "solarwinds_sd": "SolarWinds Server & Application Monitor",
        "tanium_em": "Tanium",
        "tenable_io": "Tenable IO",
        "tm_vision_one": "Trend Micro Vision One",
        "ubiquity_unifi": "Ubiquity UniFi",
        "veeam": "Veeam",
        "veritas_alta_baas": "Veritas Alta BaaS",
        "vmware_aria": "VMware Aria",
        "zscaler": "Zscaler",
        "user_input": "(Internal) User Input",
        "demo_environment": "Demo Environment",
    }
    
    # Technology ID to category mapping
    technology_categories = {
        "absolute": "endpoint_security",
        "azure_ad": "asset_source",
        "carbon_black": "endpoint_security",
        "cb_cloud": "endpoint_security",
        "cb_threat_hunter": "endpoint_security",
        "cisco_duo": "asset_source",
        "cisco_ise": "asset_source",
        "commvault": "backup_agent",
        "cortex_xdr": "endpoint_security",
        "crowdstrike_falcon": "endpoint_security",
        "cyberark_epm": "endpoint_security",
        "cylance": "endpoint_security",
        "defender_atp": "endpoint_security",
        "extrahop_revealx_360": "asset_source",
        "falcon_em": "asset_source",
        "freshworks_service": "asset_source",
        "import_hosts": "asset_source",
        "infoblox_ddi": "asset_source",
        "ivanti_neurons": "asset_source",
        "ivanti_pm": "asset_source",
        "jamf_pro": "endpoint_security",
        "ms_intune": "endpoint_security",
        "netapp_ontap": "asset_source",
        "qualys_gav": "asset_source",
        "qualys_vmpc": "vulnerability_management",
        "rapid7_insightvm": "vulnerability_management",
        "s1_ranger": "asset_source",
        "sentinel_one": "endpoint_security",
        "servicenow_cmdb": "asset_source",
        "sevco_io": "asset_source",
        "solarwinds_sd": "asset_source",
        "tanium_em": "endpoint_security",
        "tenable_io": "vulnerability_management",
        "tm_vision_one": "endpoint_security",
        "ubiquity_unifi": "asset_source",
        "veeam": "backup_agent",
        "veritas_alta_baas": "backup_agent",
        "vmware_aria": "asset_source",
        "zscaler": "asset_source",
        "user_input": "asset_source",
        "demo_environment": "asset_source",
    }
    
    # Clear existing Technology records first
    Technology.objects.all().delete()

    # Create Technology records with proper data
    for tech_id, tech_name in technology_names.items():
        Technology.objects.create(
            id=str(uuid.uuid4()),  # Generate a UUID for the id field
            technology_id=tech_id,
            name=tech_name,
            category=technology_categories.get(tech_id, 'asset_source'),
            internal=tech_id in ['user_input', 'demo_environment'],
        )


def reverse_populate_technology_data(apps, schema_editor):
    """Reverse the migration by deleting Technology records."""
    Technology = apps.get_model("integrations", "Technology")
    Technology.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0013_add_technology_fields"),
    ]

    operations = [
        # Populate the Technology table with data
        migrations.RunPython(
            populate_technology_data,
            reverse_populate_technology_data,
        ),
    ]
