# Migration to populate Technology table and update Integration references

from django.db import migrations, models
import django.db.models.deletion


def populate_technology_table(apps, schema_editor):
    """Populate Technology table with data from Integration records and hardcoded mapping."""
    Technology = apps.get_model("integrations", "Technology")
    Integration = apps.get_model("integrations", "Integration")
    
    # Technology ID to name mapping from migration 0010
    technology_names = {
        "absolute": "Absolute",
        "azure_ad": "Microsoft Entra ID (Azure AD)",
        "carbon_black": "Carbon Black",
        "cb_cloud": "Carbon Black Cloud Endpoint Standard",
        "cb_threat_hunter": "Carbon Black Threat Hunter",
        "cisco_duo": "Duo",
        "cisco_ise": "Cisco Ise AssetView",
        "commvault": "Commvault",
        "cortex_xdr": "Palo Alto Cortex XDR",
        "crowdstrike_falcon": "CrowdStrike Falcon",
        "cyberark_epm": "CyberArk Endpoint Privilege Manager",
        "cylance": "Cylance",
        "defender_atp": "Microsoft Defender for Endpoint",
        "extrahop_revealx_360": "ExtraHop RevealX 360",
        "falcon_em": "CrowdStrike Discover",
        "freshworks_service": "Freshworks Freshservice",
        "import_hosts": "Import Hosts",
        "infoblox_ddi": "Infoblox DDI",
        "ivanti_neurons": "Ivanti Neurons",
        "ivanti_pm": "Ivanti Patch Management",
        "jamf_pro": "Jamf Pro",
        "ms_intune": "Microsoft Intune",
        "netapp_ontap": "NetApp ONTAP",
        "qualys_gav": "Qualys Global AssetView",
        "qualys_vmpc": "Qualys VMDR",
        "rapid7_insightvm": "Rapid7 InsightVM",
        "s1_ranger": "SentinelOne Ranger",
        "sentinel_one": "SentinelOne",
        "servicenow_cmdb": "ServiceNow CMDB",
        "sevco_io": "Sevco",
        "solarwinds_sd": "SolarWinds Server & Application Monitor",
        "tanium_em": "Tanium",
        "tenable_io": "Tenable IO",
        "tm_vision_one": "Trend Micro Vision One",
        "ubiquity_unifi": "Ubiquity UniFi",
        "veeam": "Veeam",
        "veritas_alta_baas": "Veritas Alta BaaS",
        "vmware_aria": "VMware Aria",
        "zscaler": "Zscaler",
        "user_input": "(Internal) User Input",
        "demo_environment": "Demo Environment",
    }
    
    # Get unique technology_ids from existing Integration records
    existing_tech_ids = set(
        Integration.objects.values_list('technology_id', flat=True).distinct()
    )
    
    # Combine with hardcoded technology IDs
    all_tech_ids = existing_tech_ids.union(set(technology_names.keys()))
    
    # Create Technology records using the current table structure (id, name)
    for tech_id in all_tech_ids:
        if tech_id:  # Skip None/empty values
            Technology.objects.get_or_create(
                id=tech_id,  # Use tech_id as the id for now
                defaults={
                    'name': technology_names.get(tech_id, tech_id),
                }
            )


def reverse_populate_technology_table(apps, schema_editor):
    """Reverse the migration by deleting Technology records."""
    Technology = apps.get_model("integrations", "Technology")
    Technology.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0011_create_technology_model"),
    ]

    operations = [
        # Populate the Technology table with data
        migrations.RunPython(
            populate_technology_table,
            reverse_populate_technology_table,
        ),
    ]
