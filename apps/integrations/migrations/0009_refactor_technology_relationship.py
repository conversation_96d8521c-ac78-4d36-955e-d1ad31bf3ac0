# Consolidated migration to refactor Integration-Technology relationship
# This replaces the previous incremental migrations 0009-0014

import uuid
from django.db import migrations, models
import django.db.models.deletion


def populate_technology_data(apps, schema_editor):
    """Populate Technology table with data and update Integration references."""
    Technology = apps.get_model("integrations", "Technology")
    Integration = apps.get_model("integrations", "Integration")

    # Technology ID to name mapping
    technology_names = {
        "absolute": "Absolute",
        "azure_ad": "Microsoft Entra ID (Azure AD)",
        "carbon_black": "Carbon Black",
        "cb_cloud": "Carbon Black Cloud Endpoint Standard",
        "cb_threat_hunter": "Carbon Black Threat Hunter",
        "cisco_duo": "Duo",
        "cisco_ise": "Cisco Ise AssetView",
        "commvault": "Commvault",
        "cortex_xdr": "Palo Alto Cortex XDR",
        "crowdstrike_falcon": "CrowdStrike Falcon",
        "cyberark_epm": "CyberArk Endpoint Privilege Manager",
        "cylance": "Cylance",
        "defender_atp": "Microsoft Defender for Endpoint",
        "extrahop_revealx_360": "ExtraHop RevealX 360",
        "falcon_em": "CrowdStrike Discover",
        "freshworks_service": "Freshworks Freshservice",
        "import_hosts": "Import Hosts",
        "infoblox_ddi": "Infoblox DDI",
        "ivanti_neurons": "Ivanti Neurons",
        "ivanti_pm": "Ivanti Patch Management",
        "jamf_pro": "JAMF Pro",
        "ms_intune": "Microsoft Intune",
        "netapp_ontap": "NetApp ONTAP",
        "qualys_gav": "Qualys Global AssetView",
        "qualys_vmpc": "Qualys VMDR",
        "rapid7_insightvm": "Rapid7 InsightVM",
        "s1_ranger": "Sentinel One Singularity Ranger",
        "sentinel_one": "Sentinel One",
        "servicenow_cmdb": "ServiceNow CMDB",
        "sevco_io": "Secvo Asset Management",
        "solarwinds_sd": "SolarWinds Service Desk",
        "tanium_em": "Tanium Endpoint Management",
        "tenable_io": "Tenable IO",
        "tm_vision_one": "Trend Micro Vision One",
        "ubiquity_unifi": "Ubiquity Unifi",
        "veeam": "Veeam",
        "veritas_alta_baas": "Veritas Alta BAAS",
        "vmware_aria": "VMware Aria",
        "zscaler": "Zscaler",
        "user_input": "(Internal) User Input",
        "demo_environment": "Demo Environment",
    }
    
    # Technology categories mapping
    technology_categories = {
        "absolute": "endpoint_security",
        "azure_ad": "asset_source",
        "carbon_black": "endpoint_security",
        "cb_cloud": "endpoint_security",
        "cb_threat_hunter": "endpoint_security",
        "cisco_duo": "asset_source",
        "cisco_ise": "asset_source",
        "commvault": "backup_agent",
        "cortex_xdr": "endpoint_security",
        "crowdstrike_falcon": "endpoint_security",
        "cyberark_epm": "endpoint_security",
        "cylance": "endpoint_security",
        "defender_atp": "endpoint_security",
        "extrahop_revealx_360": "asset_source",
        "falcon_em": "asset_source",
        "freshworks_service": "asset_source",
        "import_hosts": "asset_source",
        "infoblox_ddi": "asset_source",
        "ivanti_neurons": "asset_source",
        "ivanti_pm": "patch_management",
        "jamf_pro": "asset_source",
        "ms_intune": "asset_source",
        "netapp_ontap": "asset_source",
        "qualys_gav": "asset_source",
        "qualys_vmpc": "vulnerability_management",
        "rapid7_insightvm": "vulnerability_management",
        "s1_ranger": "asset_source",
        "sentinel_one": "endpoint_security",
        "servicenow_cmdb": "asset_source",
        "sevco_io": "asset_source",
        "solarwinds_sd": "asset_source",
        "tanium_em": "endpoint_security",
        "tenable_io": "vulnerability_management",
        "tm_vision_one": "endpoint_security",
        "ubiquity_unifi": "asset_source",
        "veeam": "backup_agent",
        "veritas_alta_baas": "backup_agent",
        "vmware_aria": "asset_source",
        "zscaler": "asset_source",
        "user_input": "asset_source",
        "demo_environment": "asset_source",
    }
    
    # Get unique technology_ids from existing Integration records
    existing_tech_ids = set(
        Integration.objects.values_list('technology_id', flat=True).distinct()
    )
    
    # Combine with hardcoded technology IDs
    all_tech_ids = existing_tech_ids.union(set(technology_names.keys()))
    
    # Clear existing Technology records and recreate them properly
    Technology.objects.all().delete()

    # Create Technology records with proper structure
    for tech_id in all_tech_ids:
        if tech_id:  # Skip None/empty values
            Technology.objects.get_or_create(
                id=tech_id,  # Use tech_id as the primary key (id field)
                defaults={
                    'name': technology_names.get(tech_id, tech_id),
                }
            )


def reverse_populate_technology_data(apps, schema_editor):
    """Reverse the migration by clearing Technology references and deleting Technology records."""
    Technology = apps.get_model("integrations", "Technology")
    Integration = apps.get_model("integrations", "Integration")
    
    # Clear foreign key references
    Integration.objects.update(technology=None)
    
    # Delete Technology records
    Technology.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ("integrations", "0008_integration_endpoint_coverage_mode"),
    ]

    operations = [
        # Create Technology model
        migrations.CreateModel(
            name="Technology",
            fields=[
                (
                    "technology_id",
                    models.CharField(
                        max_length=255, primary_key=True, serialize=False, unique=True
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                ("category", models.CharField(max_length=255)),
                ("internal", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Technology",
                "verbose_name_plural": "Technologies",
                "db_table": "integrations_technology",
            },
        ),
        # Add foreign key field to Integration
        migrations.AddField(
            model_name="integration",
            name="technology",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.RESTRICT,
                to="integrations.technology",
                to_field='technology_id',
            ),
        ),
        # Populate Technology table and update Integration references
        migrations.RunPython(
            populate_technology_data,
            reverse_populate_technology_data,
        ),
        # Remove old technology_id field from Integration
        migrations.RemoveField(
            model_name="integration",
            name="technology_id",
        ),
    ]
