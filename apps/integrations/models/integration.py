from uuid import uuid4

from django.db import models


class Integration(models.Model):
    class CoverageMode(models.TextChoices):
        ENABLED = "enabled"
        IGNORE = "ignore"
        NOT_APPLICABLE = "n/a"

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.CharField(max_length=255, blank=True)
    technology_id = models.ForeignKey("Technology", on_delete=models.RESTRICT, null=True, blank=True, to_field='technology_id')
    organization = models.ForeignKey("accounts.Organization", on_delete=models.RESTRICT)
    category_id = models.CharField(max_length=255)
    enabled = models.BooleanField(default=True)
    vulnerability_coverage_mode = models.CharField(
        max_length=255,
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
    )
    endpoint_coverage_mode = models.Char<PERSON><PERSON>(
        max_length=255,
        choices=CoverageMode.choices,
        default=CoverageMode.NOT_APPLICABLE,
    )

    def __repr__(self):
        technology_id = self.technology.technology_id if self.technology else None
        return (
            f"Integration("
            f"id={self.id!r}, "
            f"technology_id={technology_id!r}, "
            f"organization={self.organization!r}, "
            f"category_id={self.category_id!r}, "
            f"enabled={self.enabled!r}, "
            f")"
        )

    def __str__(self):
        technology_id = self.technology.technology_id if self.technology else "unknown"
        return f"{self.id} [{technology_id} ({self.organization.alias})]"

    def log_extra(self):
        technology_id = self.technology.technology_id if self.technology else None
        return {
            "integration_id": str(self.id),
            "technology_id": technology_id,
            **self.organization.log_extra(),
        }
